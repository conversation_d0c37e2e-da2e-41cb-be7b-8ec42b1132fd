/* ===== CSS VARIABLES ===== */
:root {
    /* Traditional Indian Color Palette */
    --primary-gold: #D4AF37;
    --deep-maroon: #800020;
    --soft-cream: #F5F5DC;
    --calming-green: #228B22;
    --dark-gold: #B8860B;
    --light-maroon: #A0002A;
    --off-white: #FEFEFE;
    --dark-green: #006400;
    
    /* Additional Colors */
    --text-dark: #2C2C2C;
    --text-light: #666666;
    --border-light: #E0E0E0;
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.15);
    --shadow-dark: rgba(0, 0, 0, 0.3);
    
    /* Typography */
    --font-primary: 'Playfair Display', serif;
    --font-secondary: 'Inter', sans-serif;
    
    /* Spacing */
    --section-padding: 80px 0;
    --container-padding: 0 20px;
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-secondary);
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--off-white);
    overflow-x: hidden;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

a {
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
    transition: var(--transition);
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding);
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-family: var(--font-primary);
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--deep-maroon);
    margin-bottom: 15px;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-gold), var(--dark-gold));
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== HEADER & NAVIGATION ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(254, 254, 254, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px var(--shadow-light);
    z-index: 1000;
    transition: var(--transition);
}

.navbar {
    padding: 15px 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding);
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-gold);
}

.logo-text {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--deep-maroon);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 40px;
}

.nav-link {
    font-weight: 500;
    color: var(--text-dark);
    padding: 8px 0;
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gold);
    transition: var(--transition);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--deep-maroon);
    transition: var(--transition);
}

/* ===== HERO SECTION ===== */
.hero {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--deep-maroon) 0%, var(--primary-gold) 100%);
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="mandala" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="20" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/><circle cx="25" cy="25" r="15" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/><circle cx="25" cy="25" r="10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/><circle cx="25" cy="25" r="5" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23mandala)"/></svg>');
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 0 20px;
}

.hero-title {
    font-family: var(--font-primary);
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px var(--shadow-dark);
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 40px;
    opacity: 0.9;
    animation: fadeInUp 1s ease-out 0.3s both;
}

.cta-button {
    display: inline-block;
    background: var(--primary-gold);
    color: var(--deep-maroon);
    padding: 15px 40px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
    transition: var(--transition);
    animation: fadeInUp 1s ease-out 0.6s both;
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
    background: var(--dark-gold);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* ===== ABOUT VASTU SECTION ===== */
.about-vastu {
    padding: var(--section-padding);
    background: var(--soft-cream);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 20px;
    color: var(--text-dark);
}

.elements-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
    margin-top: 40px;
}

.element-item {
    text-align: center;
    padding: 20px 10px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px var(--shadow-light);
    transition: var(--transition);
}

.element-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 20px var(--shadow-medium);
}

.element-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.element-icon.earth { color: #8B4513; }
.element-icon.water { color: #4682B4; }
.element-icon.fire { color: #FF4500; }
.element-icon.air { color: #87CEEB; }
.element-icon.space { color: #9370DB; }

.element-item span {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-dark);
}

.about-image {
    text-align: center;
}

.mandala-img {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: 50%;
    box-shadow: 0 10px 30px var(--shadow-medium);
    background: var(--primary-gold);
    padding: 20px;
}

/* ===== SERVICES SECTION ===== */
.services {
    padding: var(--section-padding);
    background: white;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.service-card {
    background: white;
    padding: 40px 30px;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 20px var(--shadow-light);
    text-align: center;
    transition: var(--transition);
    border: 2px solid transparent;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px var(--shadow-medium);
    border-color: var(--primary-gold);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-gold), var(--dark-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    font-size: 2rem;
    color: white;
}

.service-title {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    color: var(--deep-maroon);
    margin-bottom: 15px;
}

.service-description {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 25px;
}

.service-button {
    background: var(--deep-maroon);
    color: white;
    padding: 12px 30px;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
}

.service-button:hover {
    background: var(--light-maroon);
    transform: translateY(-2px);
}

/* ===== VASTU TIPS SECTION ===== */
.vastu-tips {
    padding: var(--section-padding);
    background: var(--soft-cream);
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.tip-card {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: 0 3px 15px var(--shadow-light);
    transition: var(--transition);
}

.tip-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow-medium);
}

.tip-icon {
    width: 60px;
    height: 60px;
    background: var(--calming-green);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    font-size: 1.5rem;
    color: white;
}

.tip-title {
    font-family: var(--font-primary);
    font-size: 1.3rem;
    color: var(--deep-maroon);
    margin-bottom: 15px;
}

.tip-description {
    color: var(--text-light);
    line-height: 1.6;
}

/* ===== DIRECTION CHART SECTION ===== */
.direction-chart {
    padding: var(--section-padding);
    background: white;
}

.chart-container {
    overflow-x: auto;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 20px var(--shadow-light);
}

.vastu-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    font-size: 0.95rem;
}

.vastu-table th {
    background: linear-gradient(135deg, var(--deep-maroon), var(--light-maroon));
    color: white;
    padding: 20px 15px;
    text-align: left;
    font-weight: 600;
    border: 2px solid var(--primary-gold);
}

.vastu-table td {
    padding: 18px 15px;
    border: 1px solid var(--border-light);
    transition: var(--transition);
}

.vastu-table tbody tr:hover {
    background: var(--soft-cream);
    transform: scale(1.02);
}

.vastu-table tbody tr:nth-child(even) {
    background: rgba(245, 245, 220, 0.3);
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonials {
    padding: var(--section-padding);
    background: var(--soft-cream);
}

.testimonials-slider {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.testimonial-card {
    display: none;
    background: white;
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 20px var(--shadow-light);
    text-align: center;
}

.testimonial-card.active {
    display: block;
    animation: fadeIn 0.5s ease-in-out;
}

.stars {
    margin-bottom: 20px;
}

.stars i {
    color: var(--primary-gold);
    font-size: 1.2rem;
    margin: 0 2px;
}

.testimonial-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-dark);
    margin-bottom: 30px;
    font-style: italic;
}

.testimonial-author h4 {
    font-family: var(--font-primary);
    color: var(--deep-maroon);
    margin-bottom: 5px;
}

.testimonial-author span {
    color: var(--text-light);
    font-size: 0.9rem;
}

.testimonial-nav {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 30px;
}

.nav-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--border-light);
    transition: var(--transition);
}

.nav-dot.active,
.nav-dot:hover {
    background: var(--primary-gold);
}

/* ===== CONTACT SECTION ===== */
.contact {
    padding: var(--section-padding);
    background: white;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.contact-form {
    background: var(--soft-cream);
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 20px var(--shadow-light);
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--deep-maroon);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.submit-button {
    background: var(--deep-maroon);
    color: white;
    padding: 15px 40px;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 600;
    width: 100%;
    transition: var(--transition);
}

.submit-button:hover {
    background: var(--light-maroon);
    transform: translateY(-2px);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--calming-green);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    flex-shrink: 0;
}

.contact-details h4 {
    font-family: var(--font-primary);
    color: var(--deep-maroon);
    margin-bottom: 8px;
}

.contact-details p {
    color: var(--text-light);
    line-height: 1.6;
}

.whatsapp-link {
    color: var(--calming-green);
    font-weight: 500;
    text-decoration: underline;
}

.whatsapp-link:hover {
    color: var(--dark-green);
}

.map-container {
    margin-top: 20px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 3px 15px var(--shadow-light);
}

/* ===== FOOTER ===== */
.footer {
    background: var(--deep-maroon);
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.footer-logo-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-gold);
}

.footer-logo-text {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: 600;
}

.footer-description {
    line-height: 1.6;
    margin-bottom: 25px;
    opacity: 0.9;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.social-link:hover {
    background: var(--primary-gold);
    transform: translateY(-2px);
}

.footer-title {
    font-family: var(--font-primary);
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: var(--primary-gold);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    opacity: 0.9;
    transition: var(--transition);
}

.footer-links a:hover {
    opacity: 1;
    color: var(--primary-gold);
}

.footer-contact p {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    opacity: 0.9;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-bottom-links {
    display: flex;
    gap: 20px;
}

.footer-bottom-links a {
    opacity: 0.8;
    font-size: 0.9rem;
    transition: var(--transition);
}

.footer-bottom-links a:hover {
    opacity: 1;
    color: var(--primary-gold);
}

/* ===== BACK TO TOP BUTTON ===== */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--primary-gold);
    color: var(--deep-maroon);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: 1000;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--dark-gold);
    transform: translateY(-3px);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet Styles */
@media (max-width: 768px) {
    :root {
        --section-padding: 60px 0;
    }

    .section-title {
        font-size: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(254, 254, 254, 0.98);
        backdrop-filter: blur(10px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 50px;
        gap: 30px;
        transition: var(--transition);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .elements-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }

    .chart-container {
        font-size: 0.85rem;
    }

    .vastu-table th,
    .vastu-table td {
        padding: 12px 8px;
    }
}

/* Mobile Styles */
@media (max-width: 480px) {
    :root {
        --section-padding: 40px 0;
        --container-padding: 0 15px;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .cta-button {
        padding: 12px 30px;
        font-size: 1rem;
    }

    .elements-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .element-item {
        padding: 15px 8px;
    }

    .element-icon {
        font-size: 1.5rem;
    }

    .element-item span {
        font-size: 0.8rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .service-card {
        padding: 30px 20px;
    }

    .tips-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .tip-card {
        padding: 20px;
    }

    .contact-form {
        padding: 25px;
    }

    .testimonial-card {
        padding: 25px;
    }

    .testimonial-text {
        font-size: 1rem;
    }

    .footer {
        padding: 40px 0 15px;
    }

    .footer-content {
        gap: 30px;
    }

    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }

    .chart-container {
        font-size: 0.75rem;
    }

    .vastu-table th,
    .vastu-table td {
        padding: 8px 5px;
    }
}

/* Extra Small Mobile */
@media (max-width: 320px) {
    .hero-title {
        font-size: 1.6rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .elements-grid {
        grid-template-columns: 1fr;
    }

    .contact-form {
        padding: 20px;
    }

    .testimonial-card {
        padding: 20px;
    }
}

/* ===== SCROLL ANIMATIONS ===== */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    html {
        scroll-behavior: auto;
    }
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus {
    outline: 2px solid var(--primary-gold);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --shadow-light: rgba(0, 0, 0, 0.3);
        --shadow-medium: rgba(0, 0, 0, 0.4);
        --shadow-dark: rgba(0, 0, 0, 0.6);
    }
}
