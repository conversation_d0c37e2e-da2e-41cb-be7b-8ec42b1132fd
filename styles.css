/* ===== CSS VARIABLES ===== */
:root {
    /* Premium Luxury Color Palette */
    --primary-gold: #FFD700;
    --deep-gold: #DAA520;
    --royal-gold: #B8860B;
    --royal-maroon: #722F37;
    --deep-maroon: #5D1A1D;
    --light-maroon: #8B4B5C;
    --emerald-green: #50C878;
    --deep-emerald: #355E3B;
    --sage-green: #9CAF88;
    --luxury-cream: #FDF6E3;
    --pearl-white: #F8F6F0;
    --champagne: #F7E7CE;

    /* Gradient Combinations */
    --gold-gradient: linear-gradient(135deg, #FFD700 0%, #DAA520 50%, #B8860B 100%);
    --maroon-gradient: linear-gradient(135deg, #722F37 0%, #5D1A1D 50%, #8B4B5C 100%);
    --emerald-gradient: linear-gradient(135deg, #50C878 0%, #355E3B 50%, #9CAF88 100%);
    --luxury-gradient: linear-gradient(135deg, #FFD700 0%, #722F37 50%, #50C878 100%);
    --hero-gradient: linear-gradient(135deg, rgba(114, 47, 55, 0.9) 0%, rgba(93, 26, 29, 0.8) 50%, rgba(255, 215, 0, 0.7) 100%);

    /* Text Colors */
    --text-dark: #2C1810;
    --text-medium: #4A3728;
    --text-light: #6B5B47;
    --text-gold: #B8860B;
    --text-white: #FFFFFF;

    /* Border & Shadow Colors */
    --border-light: #E8DCC6;
    --border-gold: #DAA520;
    --shadow-light: rgba(114, 47, 55, 0.1);
    --shadow-medium: rgba(114, 47, 55, 0.2);
    --shadow-dark: rgba(93, 26, 29, 0.3);
    --shadow-gold: rgba(255, 215, 0, 0.3);
    --glow-gold: rgba(255, 215, 0, 0.6);
    --glow-emerald: rgba(80, 200, 120, 0.4);

    /* Typography */
    --font-primary: 'Playfair Display', serif;
    --font-secondary: 'Lato', sans-serif;
    --font-accent: 'Cinzel', serif;

    /* Spacing & Layout */
    --section-padding: 100px 0;
    --container-padding: 0 30px;
    --border-radius: 12px;
    --border-radius-large: 20px;
    --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.2s ease;
    --transition-slow: all 0.6s ease;

    /* Animation Timings */
    --animation-duration: 0.8s;
    --stagger-delay: 0.1s;
}

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-secondary);
    line-height: 1.7;
    color: var(--text-dark);
    background: var(--pearl-white);
    overflow-x: hidden;
    font-weight: 400;
    letter-spacing: 0.3px;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

a {
    text-decoration: none;
    color: inherit;
    transition: var(--transition);
}

button {
    border: none;
    background: none;
    cursor: pointer;
    font-family: inherit;
    transition: var(--transition);
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding);
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-family: var(--font-primary);
    font-size: 3rem;
    font-weight: 700;
    background: var(--gold-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
    position: relative;
    text-shadow: 0 4px 8px var(--shadow-gold);
    letter-spacing: 1px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gold-gradient);
    border-radius: 3px;
    box-shadow: 0 2px 8px var(--shadow-gold);
}

.section-title::before {
    content: '✦';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.5rem;
    color: var(--primary-gold);
    text-shadow: 0 0 10px var(--glow-gold);
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-medium);
    max-width: 700px;
    margin: 0 auto;
    font-weight: 400;
    line-height: 1.8;
    letter-spacing: 0.5px;
}

/* ===== HEADER & NAVIGATION ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(248, 246, 240, 0.95);
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 30px var(--shadow-light);
    z-index: 1000;
    transition: var(--transition);
    border-bottom: 1px solid var(--border-gold);
}

.header.scrolled {
    background: rgba(248, 246, 240, 0.98);
    box-shadow: 0 6px 40px var(--shadow-medium);
}

.navbar {
    padding: 20px 0;
    transition: var(--transition);
}

.header.scrolled .navbar {
    padding: 15px 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding);
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 15px;
    transition: var(--transition);
}

.nav-logo:hover {
    transform: scale(1.05);
}

.logo-img {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: var(--gold-gradient);
    box-shadow: 0 4px 15px var(--shadow-gold);
    border: 2px solid var(--primary-gold);
    transition: var(--transition);
}

.logo-img:hover {
    box-shadow: 0 6px 20px var(--glow-gold);
    transform: rotate(360deg);
}

.logo-text {
    font-family: var(--font-accent);
    font-size: 1.8rem;
    font-weight: 600;
    background: var(--maroon-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 1px;
    text-shadow: 0 2px 4px var(--shadow-light);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 45px;
}

.nav-link {
    font-weight: 500;
    color: var(--text-dark);
    padding: 12px 20px;
    position: relative;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-size: 1.05rem;
    letter-spacing: 0.5px;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gold-gradient);
    border-radius: var(--border-radius);
    opacity: 0;
    transition: var(--transition);
    z-index: -1;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background: var(--primary-gold);
    transition: var(--transition);
    box-shadow: 0 0 8px var(--glow-gold);
}

.nav-link:hover {
    color: var(--text-white);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px var(--shadow-gold);
}

.nav-link:hover::before {
    opacity: 1;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 60%;
}

.nav-link.active {
    color: var(--text-gold);
    font-weight: 600;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--deep-maroon);
    transition: var(--transition);
}

/* ===== HERO SECTION ===== */
.hero {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 120%;
    height: 120%;
    background: var(--hero-gradient);
    background-image:
        radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(80, 200, 120, 0.2) 0%, transparent 50%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"><defs><pattern id="mandala" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse"><g transform="translate(50,50)"><circle cx="0" cy="0" r="40" fill="none" stroke="rgba(255,215,0,0.15)" stroke-width="1"/><circle cx="0" cy="0" r="30" fill="none" stroke="rgba(255,215,0,0.1)" stroke-width="0.8"/><circle cx="0" cy="0" r="20" fill="none" stroke="rgba(255,215,0,0.08)" stroke-width="0.6"/><circle cx="0" cy="0" r="10" fill="none" stroke="rgba(255,215,0,0.05)" stroke-width="0.4"/><path d="M-40,0 L40,0 M0,-40 L0,40 M-28.3,-28.3 L28.3,28.3 M28.3,-28.3 L-28.3,28.3" stroke="rgba(255,215,0,0.1)" stroke-width="0.5"/></g></pattern></defs><rect width="200" height="200" fill="url(%23mandala)"/></svg>');
    animation: parallaxFloat 20s ease-in-out infinite;
    transform: translate(-10%, -10%);
}

@keyframes parallaxFloat {
    0%, 100% { transform: translate(-10%, -10%) scale(1); }
    50% { transform: translate(-12%, -8%) scale(1.02); }
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(ellipse at center, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0.4) 100%),
        linear-gradient(45deg, rgba(114, 47, 55, 0.3) 0%, rgba(93, 26, 29, 0.2) 100%);
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    max-width: 900px;
    padding: 0 30px;
}

.hero-title {
    font-family: var(--font-primary);
    font-size: 4.5rem;
    font-weight: 800;
    margin-bottom: 25px;
    text-shadow:
        0 4px 8px var(--shadow-dark),
        0 0 30px rgba(255, 215, 0, 0.5);
    background: linear-gradient(45deg, #FFFFFF 0%, #FFD700 50%, #FFFFFF 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 2px;
    line-height: 1.2;
    animation: fadeInUp 1s ease-out, titleGlow 3s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    0% { text-shadow: 0 4px 8px var(--shadow-dark), 0 0 30px rgba(255, 215, 0, 0.5); }
    100% { text-shadow: 0 4px 8px var(--shadow-dark), 0 0 40px rgba(255, 215, 0, 0.8); }
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 50px;
    opacity: 0.95;
    font-weight: 400;
    letter-spacing: 1px;
    text-shadow: 0 2px 4px var(--shadow-medium);
    animation: fadeInUp 1s ease-out 0.3s both;
    line-height: 1.6;
}

.cta-button {
    display: inline-block;
    background: var(--gold-gradient);
    color: var(--deep-maroon);
    padding: 20px 50px;
    font-size: 1.2rem;
    font-weight: 700;
    border-radius: var(--border-radius-large);
    box-shadow:
        0 8px 25px var(--shadow-gold),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transition: var(--transition);
    animation: fadeInUp 1s ease-out 0.6s both, ctaPulse 2s ease-in-out infinite;
    letter-spacing: 1px;
    text-transform: uppercase;
    border: 2px solid var(--primary-gold);
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: var(--transition-slow);
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow:
        0 12px 35px var(--glow-gold),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    background: var(--primary-gold);
}

@keyframes ctaPulse {
    0%, 100% { box-shadow: 0 8px 25px var(--shadow-gold), inset 0 1px 0 rgba(255, 255, 255, 0.3); }
    50% { box-shadow: 0 8px 25px var(--glow-gold), inset 0 1px 0 rgba(255, 255, 255, 0.3), 0 0 30px var(--glow-gold); }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* ===== ABOUT VASTU SECTION ===== */
.about-vastu {
    padding: var(--section-padding);
    background:
        linear-gradient(135deg, var(--luxury-cream) 0%, var(--champagne) 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 400"><defs><pattern id="lotus" x="0" y="0" width="200" height="200" patternUnits="userSpaceOnUse"><g transform="translate(100,100)" opacity="0.03"><path d="M-50,0 Q-25,-25 0,-50 Q25,-25 50,0 Q25,25 0,50 Q-25,25 -50,0 Z" fill="%23722F37"/><circle cx="0" cy="0" r="30" fill="none" stroke="%23722F37" stroke-width="0.5"/></g></pattern></defs><rect width="400" height="400" fill="url(%23lotus)"/></svg>');
    position: relative;
}

.about-vastu::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 20%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(80, 200, 120, 0.08) 0%, transparent 50%);
    pointer-events: none;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 20px;
    color: var(--text-dark);
}

.elements-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 25px;
    margin-top: 50px;
}

.element-item {
    text-align: center;
    padding: 30px 15px;
    background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 246, 240, 0.8) 100%);
    border-radius: var(--border-radius-large);
    box-shadow:
        0 8px 25px var(--shadow-light),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: var(--transition);
    border: 1px solid var(--border-gold);
    position: relative;
    overflow: hidden;
}

.element-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gold-gradient);
    opacity: 0;
    transition: var(--transition);
    z-index: 0;
}

.element-item:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow:
        0 15px 40px var(--shadow-medium),
        0 0 30px var(--glow-gold);
}

.element-item:hover::before {
    opacity: 0.1;
}

.element-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    position: relative;
    z-index: 1;
    transition: var(--transition);
}

.element-item:hover .element-icon {
    transform: scale(1.2) rotate(10deg);
    filter: drop-shadow(0 4px 8px var(--shadow-gold));
}

.element-icon.earth {
    color: #8B4513;
    text-shadow: 0 0 20px rgba(139, 69, 19, 0.5);
}
.element-icon.water {
    color: #4682B4;
    text-shadow: 0 0 20px rgba(70, 130, 180, 0.5);
}
.element-icon.fire {
    color: #FF4500;
    text-shadow: 0 0 20px rgba(255, 69, 0, 0.5);
}
.element-icon.air {
    color: #87CEEB;
    text-shadow: 0 0 20px rgba(135, 206, 235, 0.5);
}
.element-icon.space {
    color: #9370DB;
    text-shadow: 0 0 20px rgba(147, 112, 219, 0.5);
}

.element-item span {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    position: relative;
    z-index: 1;
    letter-spacing: 0.5px;
}

.about-image {
    text-align: center;
}

.mandala-img {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: 50%;
    box-shadow: 0 10px 30px var(--shadow-medium);
    background: var(--primary-gold);
    padding: 20px;
}

/* ===== SERVICES SECTION ===== */
.services {
    padding: var(--section-padding);
    background:
        linear-gradient(135deg, var(--pearl-white) 0%, rgba(248, 246, 240, 0.8) 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 300"><defs><pattern id="geometric" x="0" y="0" width="150" height="150" patternUnits="userSpaceOnUse"><g transform="translate(75,75)" opacity="0.02"><polygon points="0,-50 43.3,-25 43.3,25 0,50 -43.3,25 -43.3,-25" fill="%23722F37"/><circle cx="0" cy="0" r="25" fill="none" stroke="%23722F37" stroke-width="0.5"/></g></pattern></defs><rect width="300" height="300" fill="url(%23geometric)"/></svg>');
    position: relative;
}

.services::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 30% 70%, rgba(80, 200, 120, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(255, 215, 0, 0.08) 0%, transparent 50%);
    pointer-events: none;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 40px;
    position: relative;
    z-index: 1;
}

.service-card {
    background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 246, 240, 0.9) 100%);
    padding: 50px 35px;
    border-radius: var(--border-radius-large);
    box-shadow:
        0 10px 30px var(--shadow-light),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    text-align: center;
    transition: var(--transition);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gold-gradient);
    opacity: 0;
    transition: var(--transition);
    z-index: 0;
}

.service-card:hover {
    transform: translateY(-15px) scale(1.03);
    box-shadow:
        0 25px 60px var(--shadow-medium),
        0 0 40px var(--glow-gold);
    border-color: var(--primary-gold);
}

.service-card:hover::before {
    opacity: 0.08;
}

.service-icon {
    width: 90px;
    height: 90px;
    background: var(--gold-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 30px;
    font-size: 2.2rem;
    color: var(--deep-maroon);
    transition: var(--transition);
    position: relative;
    z-index: 1;
    box-shadow:
        0 8px 20px var(--shadow-gold),
        inset 0 2px 0 rgba(255, 255, 255, 0.3);
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow:
        0 12px 30px var(--glow-gold),
        inset 0 2px 0 rgba(255, 255, 255, 0.4);
}

.service-title {
    font-family: var(--font-primary);
    font-size: 1.7rem;
    font-weight: 600;
    background: var(--maroon-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
    position: relative;
    z-index: 1;
    letter-spacing: 0.5px;
}

.service-description {
    color: var(--text-medium);
    line-height: 1.8;
    margin-bottom: 35px;
    font-size: 1.05rem;
    position: relative;
    z-index: 1;
}

.service-button {
    background: var(--maroon-gradient);
    color: white;
    padding: 15px 35px;
    border-radius: var(--border-radius-large);
    font-weight: 600;
    transition: var(--transition);
    position: relative;
    z-index: 1;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    font-size: 0.95rem;
    box-shadow: 0 4px 15px var(--shadow-medium);
    border: 2px solid transparent;
}

.service-button:hover {
    background: var(--emerald-gradient);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px var(--glow-emerald);
    border-color: var(--emerald-green);
}

/* ===== VASTU TIPS SECTION ===== */
.vastu-tips {
    padding: var(--section-padding);
    background: var(--soft-cream);
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.tip-card {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: 0 3px 15px var(--shadow-light);
    transition: var(--transition);
}

.tip-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow-medium);
}

.tip-icon {
    width: 60px;
    height: 60px;
    background: var(--calming-green);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    font-size: 1.5rem;
    color: white;
}

.tip-title {
    font-family: var(--font-primary);
    font-size: 1.3rem;
    color: var(--deep-maroon);
    margin-bottom: 15px;
}

.tip-description {
    color: var(--text-light);
    line-height: 1.6;
}

/* ===== DIRECTION CHART SECTION ===== */
.direction-chart {
    padding: var(--section-padding);
    background:
        linear-gradient(135deg, var(--pearl-white) 0%, var(--luxury-cream) 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 500"><defs><pattern id="compass" x="0" y="0" width="250" height="250" patternUnits="userSpaceOnUse"><g transform="translate(125,125)" opacity="0.03"><circle cx="0" cy="0" r="100" fill="none" stroke="%23722F37" stroke-width="1"/><path d="M0,-100 L20,-80 L0,-60 L-20,-80 Z" fill="%23722F37"/><path d="M100,0 L80,20 L60,0 L80,-20 Z" fill="%23722F37"/><path d="M0,100 L20,80 L0,60 L-20,80 Z" fill="%23722F37"/><path d="M-100,0 L-80,20 L-60,0 L-80,-20 Z" fill="%23722F37"/></g></pattern></defs><rect width="500" height="500" fill="url(%23compass)"/></svg>');
    position: relative;
}

.direction-chart::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 25% 25%, rgba(255, 215, 0, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(80, 200, 120, 0.06) 0%, transparent 50%);
    pointer-events: none;
}

.chart-container {
    overflow-x: auto;
    border-radius: var(--border-radius-large);
    box-shadow:
        0 15px 40px var(--shadow-medium),
        0 0 30px var(--glow-gold);
    position: relative;
    z-index: 1;
    border: 3px solid var(--border-gold);
}

.vastu-table {
    width: 100%;
    border-collapse: collapse;
    background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 246, 240, 0.9) 100%);
    font-size: 1rem;
    position: relative;
}

.vastu-table th {
    background: var(--maroon-gradient);
    color: white;
    padding: 25px 20px;
    text-align: center;
    font-weight: 700;
    font-family: var(--font-primary);
    font-size: 1.1rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    border: 2px solid var(--primary-gold);
    position: relative;
}

.vastu-table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--gold-gradient);
}

.vastu-table td {
    padding: 20px 18px;
    border: 1px solid var(--border-gold);
    transition: var(--transition);
    text-align: center;
    font-weight: 500;
    position: relative;
}

.vastu-table td:first-child {
    font-weight: 700;
    font-family: var(--font-primary);
    font-size: 1.1rem;
    background: var(--gold-gradient);
    color: var(--deep-maroon);
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

.vastu-table tbody tr:hover {
    background: var(--champagne);
    transform: scale(1.02);
    box-shadow: 0 8px 25px var(--shadow-light);
}

.vastu-table tbody tr:nth-child(even) {
    background: rgba(253, 246, 227, 0.5);
}

.vastu-table tbody tr:hover td {
    color: var(--text-dark);
    text-shadow: 0 1px 2px rgba(255, 215, 0, 0.3);
}

/* Direction Icons */
.direction-icon {
    display: inline-block;
    margin-right: 8px;
    font-size: 1.2rem;
    color: var(--primary-gold);
    text-shadow: 0 0 10px var(--glow-gold);
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonials {
    padding: var(--section-padding);
    background:
        linear-gradient(135deg, var(--champagne) 0%, var(--luxury-cream) 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 600 600"><defs><pattern id="testimonial" x="0" y="0" width="300" height="300" patternUnits="userSpaceOnUse"><g transform="translate(150,150)" opacity="0.02"><path d="M-100,-50 Q-50,-100 0,-50 Q50,-100 100,-50 Q50,0 100,50 Q50,100 0,50 Q-50,100 -100,50 Q-50,0 -100,-50 Z" fill="%23722F37"/><circle cx="0" cy="0" r="75" fill="none" stroke="%23722F37" stroke-width="1"/></g></pattern></defs><rect width="600" height="600" fill="url(%23testimonial)"/></svg>');
    position: relative;
}

.testimonials::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 40% 60%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 60% 40%, rgba(80, 200, 120, 0.08) 0%, transparent 50%);
    pointer-events: none;
}

.testimonials-slider {
    position: relative;
    max-width: 900px;
    margin: 0 auto;
    z-index: 1;
}

.testimonial-card {
    display: none;
    background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 246, 240, 0.9) 100%);
    padding: 60px 50px;
    border-radius: var(--border-radius-large);
    box-shadow:
        0 20px 50px var(--shadow-medium),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    text-align: center;
    border: 2px solid var(--border-gold);
    position: relative;
    overflow: hidden;
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: 20px;
    left: 30px;
    font-size: 6rem;
    color: var(--primary-gold);
    opacity: 0.3;
    font-family: var(--font-primary);
    line-height: 1;
}

.testimonial-card::after {
    content: '"';
    position: absolute;
    bottom: 20px;
    right: 30px;
    font-size: 6rem;
    color: var(--primary-gold);
    opacity: 0.3;
    font-family: var(--font-primary);
    line-height: 1;
    transform: rotate(180deg);
}

.testimonial-card.active {
    display: block;
    animation: testimonialFadeIn 0.8s ease-in-out;
}

@keyframes testimonialFadeIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.stars {
    margin-bottom: 25px;
    position: relative;
    z-index: 1;
}

.stars i {
    color: var(--primary-gold);
    font-size: 1.4rem;
    margin: 0 3px;
    text-shadow: 0 0 10px var(--glow-gold);
    transition: var(--transition);
}

.stars i:hover {
    transform: scale(1.2);
}

.testimonial-text {
    font-size: 1.3rem;
    line-height: 1.9;
    color: var(--text-dark);
    margin-bottom: 40px;
    font-style: italic;
    font-weight: 400;
    position: relative;
    z-index: 1;
    letter-spacing: 0.3px;
}

.testimonial-author {
    position: relative;
    z-index: 1;
}

.testimonial-author h4 {
    font-family: var(--font-primary);
    font-size: 1.4rem;
    background: var(--maroon-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
    font-weight: 600;
}

.testimonial-author span {
    color: var(--text-medium);
    font-size: 1rem;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.testimonial-nav {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 40px;
    position: relative;
    z-index: 1;
}

.nav-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--border-light);
    transition: var(--transition);
    cursor: pointer;
    border: 2px solid transparent;
    position: relative;
}

.nav-dot::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-gold);
    opacity: 0;
    transition: var(--transition);
}

.nav-dot.active,
.nav-dot:hover {
    background: var(--gold-gradient);
    border-color: var(--primary-gold);
    box-shadow: 0 0 15px var(--glow-gold);
}

.nav-dot.active::before,
.nav-dot:hover::before {
    opacity: 1;
}

/* ===== CONTACT SECTION ===== */
.contact {
    padding: var(--section-padding);
    background: white;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.contact-form {
    background: var(--soft-cream);
    padding: 40px;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 20px var(--shadow-light);
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--deep-maroon);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.submit-button {
    background: var(--deep-maroon);
    color: white;
    padding: 15px 40px;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 600;
    width: 100%;
    transition: var(--transition);
}

.submit-button:hover {
    background: var(--light-maroon);
    transform: translateY(-2px);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--calming-green);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    flex-shrink: 0;
}

.contact-details h4 {
    font-family: var(--font-primary);
    color: var(--deep-maroon);
    margin-bottom: 8px;
}

.contact-details p {
    color: var(--text-light);
    line-height: 1.6;
}

.whatsapp-link {
    color: var(--calming-green);
    font-weight: 500;
    text-decoration: underline;
}

.whatsapp-link:hover {
    color: var(--dark-green);
}

.map-container {
    margin-top: 20px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 3px 15px var(--shadow-light);
}

/* ===== FOOTER ===== */
.footer {
    background: var(--deep-maroon);
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.footer-logo-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-gold);
}

.footer-logo-text {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: 600;
}

.footer-description {
    line-height: 1.6;
    margin-bottom: 25px;
    opacity: 0.9;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.social-link:hover {
    background: var(--primary-gold);
    transform: translateY(-2px);
}

.footer-title {
    font-family: var(--font-primary);
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: var(--primary-gold);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    opacity: 0.9;
    transition: var(--transition);
}

.footer-links a:hover {
    opacity: 1;
    color: var(--primary-gold);
}

.footer-contact p {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    opacity: 0.9;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-bottom-links {
    display: flex;
    gap: 20px;
}

.footer-bottom-links a {
    opacity: 0.8;
    font-size: 0.9rem;
    transition: var(--transition);
}

.footer-bottom-links a:hover {
    opacity: 1;
    color: var(--primary-gold);
}

/* ===== FLOATING ACTION BUTTONS ===== */
.floating-buttons {
    position: fixed;
    bottom: 30px;
    right: 30px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    z-index: 1000;
}

.whatsapp-float,
.music-toggle,
.back-to-top {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    transition: var(--transition);
    box-shadow: 0 4px 20px var(--shadow-medium);
    position: relative;
    overflow: hidden;
}

/* WhatsApp Button */
.whatsapp-float {
    background: var(--emerald-gradient);
    color: white;
    text-decoration: none;
    animation: whatsappPulse 2s ease-in-out infinite;
}

.whatsapp-float:hover {
    transform: scale(1.1) translateY(-3px);
    box-shadow: 0 8px 30px var(--glow-emerald);
}

@keyframes whatsappPulse {
    0%, 100% { box-shadow: 0 4px 20px var(--shadow-medium); }
    50% { box-shadow: 0 4px 20px var(--glow-emerald), 0 0 20px var(--glow-emerald); }
}

/* Music Toggle Button */
.music-toggle {
    background: var(--maroon-gradient);
    color: white;
    border: none;
    cursor: pointer;
}

.music-toggle:hover {
    transform: scale(1.1) translateY(-3px);
    box-shadow: 0 8px 30px var(--shadow-dark);
}

.music-toggle.playing {
    animation: musicPulse 1s ease-in-out infinite;
}

@keyframes musicPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Back to Top Button */
.back-to-top {
    background: var(--gold-gradient);
    color: var(--deep-maroon);
    border: none;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: scale(1.1) translateY(-3px);
    box-shadow: 0 8px 30px var(--glow-gold);
}

/* Tooltips for Floating Buttons */
.whatsapp-tooltip,
.music-tooltip,
.back-to-top-tooltip {
    position: absolute;
    right: 70px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--text-dark);
    color: white;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    font-size: 0.85rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    pointer-events: none;
}

.whatsapp-tooltip::after,
.music-tooltip::after,
.back-to-top-tooltip::after {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border: 5px solid transparent;
    border-left-color: var(--text-dark);
}

.whatsapp-float:hover .whatsapp-tooltip,
.music-toggle:hover .music-tooltip,
.back-to-top:hover .back-to-top-tooltip {
    opacity: 1;
    visibility: visible;
    right: 75px;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet Styles */
@media (max-width: 768px) {
    :root {
        --section-padding: 80px 0;
        --container-padding: 0 25px;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .hero-title {
        font-size: 3.5rem;
        letter-spacing: 1px;
    }

    .hero-subtitle {
        font-size: 1.3rem;
    }

    .cta-button {
        padding: 18px 45px;
        font-size: 1.1rem;
    }

    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(254, 254, 254, 0.98);
        backdrop-filter: blur(10px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 50px;
        gap: 30px;
        transition: var(--transition);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .elements-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }

    .chart-container {
        font-size: 0.85rem;
    }

    .vastu-table th,
    .vastu-table td {
        padding: 12px 8px;
    }
}

/* Mobile Styles */
@media (max-width: 480px) {
    :root {
        --section-padding: 60px 0;
        --container-padding: 0 20px;
    }

    .section-title {
        font-size: 2.2rem;
    }

    .hero-title {
        font-size: 2.8rem;
        letter-spacing: 1px;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .cta-button {
        padding: 16px 35px;
        font-size: 1.1rem;
    }

    .floating-buttons {
        bottom: 20px;
        right: 20px;
        gap: 12px;
    }

    .whatsapp-float,
    .music-toggle,
    .back-to-top {
        width: 55px;
        height: 55px;
        font-size: 1.3rem;
    }

    .testimonial-card {
        padding: 40px 30px;
    }

    .testimonial-card::before,
    .testimonial-card::after {
        font-size: 4rem;
    }

    .elements-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .element-item {
        padding: 15px 8px;
    }

    .element-icon {
        font-size: 1.5rem;
    }

    .element-item span {
        font-size: 0.8rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .service-card {
        padding: 30px 20px;
    }

    .tips-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .tip-card {
        padding: 20px;
    }

    .contact-form {
        padding: 30px 25px;
    }

    .testimonial-text {
        font-size: 1rem;
    }

    .footer {
        padding: 40px 0 15px;
    }

    .footer-content {
        gap: 30px;
    }

    .back-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }

    .chart-container {
        font-size: 0.75rem;
    }

    .vastu-table th,
    .vastu-table td {
        padding: 8px 5px;
    }
}

/* Extra Small Mobile */
@media (max-width: 320px) {
    .hero-title {
        font-size: 1.6rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .elements-grid {
        grid-template-columns: 1fr;
    }

    .contact-form {
        padding: 20px;
    }

    .testimonial-card {
        padding: 20px;
    }
}

/* ===== SCROLL ANIMATIONS ===== */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    html {
        scroll-behavior: auto;
    }
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus {
    outline: 2px solid var(--primary-gold);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --shadow-light: rgba(0, 0, 0, 0.3);
        --shadow-medium: rgba(0, 0, 0, 0.4);
        --shadow-dark: rgba(0, 0, 0, 0.6);
    }
}
